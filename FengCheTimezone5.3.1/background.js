importScripts("user-data-manager.js");let currentIconState="inactive",flashInterval=null;async function setIconState(e){if(currentIconState!==e){currentIconState=e,flashInterval&&(clearInterval(flashInterval),flashInterval=null);try{switch(e){case"inactive":await chrome.action.setBadgeText({text:""}),await chrome.action.setBadgeBackgroundColor({color:"#888888"}),await chrome.action.setTitle({title:"Global Timezone & Location Spoofer - 未启用"});break;case"active":await chrome.action.setBadgeText({text:"●"}),await chrome.action.setBadgeBackgroundColor({color:"#28a745"}),await chrome.action.setTitle({title:"Global Timezone & Location Spoofer - 已启用"});break;case"working":await chrome.action.setTitle({title:"Global Timezone & Location Spoofer - 工作中"});let e=!1;flashInterval=setInterval(async()=>{try{e?(await chrome.action.setBadgeText({text:"◉"}),await chrome.action.setBadgeBackgroundColor({color:"#00ff00"})):(await chrome.action.setBadgeText({text:"●"}),await chrome.action.setBadgeBackgroundColor({color:"#28a745"})),e=!e}catch(e){console.warn(1501)}},500)}console.log(1301)}catch(e){console.warn(1502)}}}chrome.runtime.onInstalled.addListener(()=>{console.log(1001),console.log(1002),console.log(1003),setIconState("inactive"),chrome.storage.sync.get(["selectedRegion"],e=>{e.selectedRegion||(console.log(1004),chrome.storage.sync.set({selectedRegion:"US",lastUpdate:Date.now()}))}),chrome.action.setPopup({popup:"auth.html"})}),chrome.runtime.onStartup.addListener(()=>{console.log(1005),setIconState("inactive"),checkAuthAndSetUI()}),chrome.runtime.onMessage.addListener((e,o,t)=>{if("AUTH_STATUS_CHANGE"===e.type)e.authorized&&e.userData?(generateAuthFile(e.userData),chrome.action.setPopup({popup:"popup.html"})):(removeAuthFile(),chrome.action.setPopup({popup:"auth.html"}),setIconState("inactive")),t({success:!0});else{if("CHECK_AUTH_STATUS"===e.type)return checkAuthAndSetUI().then(()=>{t({success:!0})}),!0;if("DISABLE_PLUGIN_FOR_EXPIRED_USER"===e.type)return disablePluginForExpiredUser().then(()=>{t({success:!0})}),!0;if("APPLY_PLUGIN_SETTINGS"===e.type)return applyPluginSettingsToAllTabs(e.cityData).then(()=>{t({success:!0})}),!0;if("PLUGIN_ACTIVATED"===e.type)setIconState("active"),t({success:!0});else if("PLUGIN_DEACTIVATED"===e.type)setIconState("inactive"),t({success:!0});else if("PLUGIN_WORKING"===e.type)setIconState("working"),t({success:!0});else{if("HANDLE_EXPIRED_USER"===e.type)return handleExpiredUserFromPopup().then(e=>{t({success:e})}),!0;if("CLEAR_EXPIRED_USER_SETTINGS"===e.type)return clearExpiredUserSettings().then(e=>{t({success:e})}),!0}}});let hasCheckedAuthThisSession=!1;async function checkAuthAndSetUI(){if(hasCheckedAuthThisSession)console.log("⏭️ 本次会话已检查过认证状态，跳过");else try{console.log(1290),hasCheckedAuthThisSession=!0;if(!await userDataManager.isLoggedIn())return console.log(1102),void chrome.action.setPopup({popup:"auth.html"});const e=await userDataManager.validateWithServer();if(e&&e.remaining_days>0&&"active"===e.status){console.log(1101);const o=userDataManager.calculateRealTimeExpiration(e);o.remainingDays;if(console.log(1201),console.log(1202),console.log(1203),o.expireDate)console.log(1207);else if(e.expires_at)try{new Date(e.expires_at).toLocaleString();console.log(1207)}catch(e){console.log(1207)}console.log(1204),console.log(1205),await generateAuthFile(e),chrome.action.setPopup({popup:"popup.html"})}else console.log(1102),e&&(console.log(1103),console.log(1202),console.log(1203),console.log(1204)),await disablePluginForExpiredUser(),chrome.action.setPopup({popup:"auth.html"})}catch(e){console.error(1504);const o=await getStoredUserData();o&&o.user_id?(console.log(1302),chrome.action.setPopup({popup:"popup.html"})):chrome.action.setPopup({popup:"auth.html"})}}async function generateAuthFile(e){try{console.log(1240),console.log(1241),console.log(1242)}catch(e){console.error(1504)}}async function removeAuthFile(){try{console.log(1250),await userDataManager.clear(),console.log(1251)}catch(e){console.error(1504)}}async function getStoredUserData(){return await userDataManager.get()}async function disablePluginForExpiredUser(){try{console.log(1104),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log(1260);try{await chrome.storage.session.set({plugin_globally_activated:!1}),console.log(1261)}catch(e){console.log(1262)}const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("plugin_auth_status"),localStorage.removeItem("plugin_user_info"),localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),localStorage.setItem("plugin_expired_handled","true"),console.log(1104)}})}catch(e){}console.log(1263)}catch(e){console.error(1504)}}async function handleExpiredUserFromPopup(){try{console.log(1270),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]);const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),localStorage.removeItem("plugin_user_info"),localStorage.removeItem("plugin_auth_status"),localStorage.setItem("plugin_expired_handled","true")}})}catch(e){}return!0}catch(e){return console.error(1504),!1}}async function clearExpiredUserSettings(){try{return console.log(1280),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log(1281),!0}catch(e){return console.error(1504),!1}}async function applyPluginSettingsToAllTabs(e){try{await chrome.storage.sync.set({selectedCity:e}),console.log(1290)}catch(e){console.error(1504)}}chrome.storage.onChanged.addListener((e,o)=>{"sync"===o&&e.selectedCity&&(console.log(1302),chrome.tabs.query({},tabs=>{const o=e.selectedCity.newValue;tabs.forEach(e=>{try{chrome.scripting.executeScript({target:{tabId:e.id},func:(key,e)=>{localStorage.setItem(key,JSON.stringify(e)),console.log(1303)},args:["globalTimezoneSpoofer_selectedCity",o]}).catch(()=>{})}catch(e){}})}))});