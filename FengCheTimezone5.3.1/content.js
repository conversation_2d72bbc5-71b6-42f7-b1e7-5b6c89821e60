!function(){"use strict";if(window.WEBRTC_INTERCEPTOR_LOADED)return;if(window.WEBRTC_INTERCEPTOR_LOADED=!0,console.log(1006),console.log(1007),"true"===sessionStorage.getItem("plugin_user_expired"))return void console.log(1105);const t={"America/New_York":[{city:"New York",state:"NY",country:"United States",country_code:"US",lat:40.7128,lng:-74.006,locale:"en-US"},{city:"Boston",state:"MA",country:"United States",country_code:"US",lat:42.3601,lng:-71.0589,locale:"en-US"},{city:"Philadelphia",state:"PA",country:"United States",country_code:"US",lat:39.9526,lng:-75.1652,locale:"en-US"},{city:"Atlanta",state:"GA",country:"United States",country_code:"US",lat:33.749,lng:-84.388,locale:"en-US"},{city:"Miami",state:"FL",country:"United States",country_code:"US",lat:25.7617,lng:-80.1918,locale:"en-US"},{city:"Washington",state:"DC",country:"United States",country_code:"US",lat:38.9072,lng:-77.0369,locale:"en-US"},{city:"Charlotte",state:"NC",country:"United States",country_code:"US",lat:35.2271,lng:-80.8431,locale:"en-US"},{city:"Jacksonville",state:"FL",country:"United States",country_code:"US",lat:30.3322,lng:-81.6557,locale:"en-US"}],"America/Chicago":[{city:"Chicago",state:"IL",country:"United States",country_code:"US",lat:41.8781,lng:-87.6298,locale:"en-US"},{city:"Houston",state:"TX",country:"United States",country_code:"US",lat:29.7604,lng:-95.3698,locale:"en-US"},{city:"Dallas",state:"TX",country:"United States",country_code:"US",lat:32.7767,lng:-96.797,locale:"en-US"},{city:"San Antonio",state:"TX",country:"United States",country_code:"US",lat:29.4241,lng:-98.4936,locale:"en-US"},{city:"Austin",state:"TX",country:"United States",country_code:"US",lat:30.2672,lng:-97.7431,locale:"en-US"},{city:"Minneapolis",state:"MN",country:"United States",country_code:"US",lat:44.9778,lng:-93.265,locale:"en-US"},{city:"Kansas City",state:"MO",country:"United States",country_code:"US",lat:39.0997,lng:-94.5786,locale:"en-US"},{city:"Nashville",state:"TN",country:"United States",country_code:"US",lat:36.1627,lng:-86.7816,locale:"en-US"}],"America/Denver":[{city:"Denver",state:"CO",country:"United States",country_code:"US",lat:39.7392,lng:-104.9903,locale:"en-US"},{city:"Phoenix",state:"AZ",country:"United States",country_code:"US",lat:33.4484,lng:-112.074,locale:"en-US"},{city:"Salt Lake City",state:"UT",country:"United States",country_code:"US",lat:40.7608,lng:-111.891,locale:"en-US"},{city:"Albuquerque",state:"NM",country:"United States",country_code:"US",lat:35.0844,lng:-106.6504,locale:"en-US"},{city:"Colorado Springs",state:"CO",country:"United States",country_code:"US",lat:38.8339,lng:-104.8214,locale:"en-US"},{city:"Mesa",state:"AZ",country:"United States",country_code:"US",lat:33.4152,lng:-111.8315,locale:"en-US"},{city:"Tucson",state:"AZ",country:"United States",country_code:"US",lat:32.2226,lng:-110.9747,locale:"en-US"}],"America/Los_Angeles":[{city:"Los Angeles",state:"CA",country:"United States",country_code:"US",lat:34.0522,lng:-118.2437,locale:"en-US"},{city:"San Francisco",state:"CA",country:"United States",country_code:"US",lat:37.7749,lng:-122.4194,locale:"en-US"},{city:"Seattle",state:"WA",country:"United States",country_code:"US",lat:47.6062,lng:-122.3321,locale:"en-US"},{city:"San Diego",state:"CA",country:"United States",country_code:"US",lat:32.7157,lng:-117.1611,locale:"en-US"},{city:"Las Vegas",state:"NV",country:"United States",country_code:"US",lat:36.1699,lng:-115.1398,locale:"en-US"},{city:"Portland",state:"OR",country:"United States",country_code:"US",lat:45.5152,lng:-122.6784,locale:"en-US"},{city:"Sacramento",state:"CA",country:"United States",country_code:"US",lat:38.5816,lng:-121.4944,locale:"en-US"},{city:"San Jose",state:"CA",country:"United States",country_code:"US",lat:37.3382,lng:-121.8863,locale:"en-US"}],"Asia/Taipei":[{city:"Taipei",state:"Taipei City",country:"Taiwan",country_code:"TW",lat:25.033,lng:121.5654,locale:"zh-TW"},{city:"Kaohsiung",state:"Kaohsiung City",country:"Taiwan",country_code:"TW",lat:22.6273,lng:120.3014,locale:"zh-TW"},{city:"Taichung",state:"Taichung City",country:"Taiwan",country_code:"TW",lat:24.1477,lng:120.6736,locale:"zh-TW"},{city:"Tainan",state:"Tainan City",country:"Taiwan",country_code:"TW",lat:22.9999,lng:120.2269,locale:"zh-TW"},{city:"Taoyuan",state:"Taoyuan City",country:"Taiwan",country_code:"TW",lat:24.9936,lng:121.301,locale:"zh-TW"}],"Asia/Tokyo":[{city:"Tokyo",state:"Tokyo",country:"Japan",country_code:"JP",lat:35.6762,lng:139.6503,locale:"ja-JP"},{city:"Osaka",state:"Osaka",country:"Japan",country_code:"JP",lat:34.6937,lng:135.5023,locale:"ja-JP"},{city:"Yokohama",state:"Kanagawa",country:"Japan",country_code:"JP",lat:35.4437,lng:139.638,locale:"ja-JP"},{city:"Nagoya",state:"Aichi",country:"Japan",country_code:"JP",lat:35.1815,lng:136.9066,locale:"ja-JP"},{city:"Sapporo",state:"Hokkaido",country:"Japan",country_code:"JP",lat:43.0642,lng:141.3469,locale:"ja-JP"},{city:"Fukuoka",state:"Fukuoka",country:"Japan",country_code:"JP",lat:33.5904,lng:130.4017,locale:"ja-JP"},{city:"Kobe",state:"Hyogo",country:"Japan",country_code:"JP",lat:34.6901,lng:135.1956,locale:"ja-JP"},{city:"Kyoto",state:"Kyoto",country:"Japan",country_code:"JP",lat:35.0116,lng:135.7681,locale:"ja-JP"}],"Asia/Singapore":[{city:"Singapore",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3521,lng:103.8198,locale:"en-SG"},{city:"Jurong West",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3404,lng:103.709,locale:"en-SG"},{city:"Woodlands",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.4382,lng:103.789,locale:"en-SG"},{city:"Tampines",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3496,lng:103.9568,locale:"en-SG"},{city:"Sengkang",state:"Singapore",country:"Singapore",country_code:"SG",lat:1.3868,lng:103.8914,locale:"en-SG"}]};let e,o;const n="globalTimezoneSpoofer_fingerprint",a=localStorage.getItem("globalTimezoneSpoofer_selectedCity");let c,l=localStorage.getItem(n);if(a)try{o=JSON.parse(a),e=o.timezone,console.log("🎯 使用保存的地区设置:",o.country,o.city)}catch(t){console.log("⚠️ 解析保存设置失败，使用随机选择"),o=null}if(!o){const n=Object.keys(t);e=n[Math.floor(Math.random()*n.length)];const a=t[e];o=a[Math.floor(Math.random()*a.length)],console.log("🎲 使用随机地区设置（无保存偏好）")}if(l)try{c=JSON.parse(l),console.log("🔒 使用保存的指纹数据")}catch(t){c=null}c||(c={latOffset:.2*(Math.random()-.5),lngOffset:.2*(Math.random()-.5),accuracyVariation:Math.floor(50*Math.random())+20,deviceMemory:[4,8,16,32][Math.floor(4*Math.random())],webglVendor:["Intel Inc.","NVIDIA Corporation","AMD","Apple Inc."][Math.floor(4*Math.random())],webglRenderer:["Intel Iris OpenGL Engine","NVIDIA GeForce GTX 1060","AMD Radeon Pro 560","Apple M1","Intel HD Graphics 620"][Math.floor(5*Math.random())],webrtcIPRange:["8.8.8.","8.8.4.","1.1.1.","1.0.0.","208.67.222.","208.67.220.","4.2.2.","4.2.1.","173.252.","31.13.","172.217.","142.250."][Math.floor(10*Math.random())],webrtcLastOctet:Math.floor(254*Math.random())+1},localStorage.setItem(n,JSON.stringify(c)),console.log(1610));c.latOffset,c.lngOffset;const i=c.accuracyVariation;let r=null,s=null,g=!1,d=!0;navigator.mediaDevices&&navigator.mediaDevices.getUserMedia&&(s=navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices));const u=function(t){try{const now=new Date,e=now.getTime()+6e4*now.getTimezoneOffset(),o=new Intl.DateTimeFormat("en",{timeZone:t,timeZoneName:"longOffset"}),n=o.formatToParts(now).find(t=>"timeZoneName"===t.type);if(n&&n.value){const t=n.value.match(/GMT([+-])(\d{1,2})(?::?(\d{2}))?/);if(t){const e="+"===t[1]?1:-1,o=parseInt(t[2]);return e*(60*o+parseInt(t[3]||"0"))}}const a=(new Date(now.toLocaleString("en-US",{timeZone:t})).getTime()-e)/6e4;return Math.round(a)}catch(e){console.warn("计算时区偏移失败",t,e);return{"America/New_York":-300,"America/Chicago":-360,"America/Denver":-420,"America/Los_Angeles":-480,"Asia/Tokyo":540,"Asia/Taipei":480,"Asia/Singapore":480}[t]||0}}(e),y=.01*(Math.random()-.5),S=.01*(Math.random()-.5),p=parseFloat((o.lat+y).toFixed(6)),m=parseFloat((o.lng+S).toFixed(6));console.log("🎯 生成固定坐标:",p,m,"(无跳动)");const U={timezone:e,timezone_name:e.split("/")[1].replace("_"," ")+" Time",latitude:p,longitude:m,city:o.city,state:o.state,country:o.country,country_code:o.country_code,locale:o.locale,accuracy:i,offset:-u/60,dst:!0};window.addEventListener("message",t=>{t.data&&"DISABLE_PLUGIN_EXPIRED"===t.data.type?(console.log(1620),console.log(1621),console.log(1622),console.log(1623),function(){console.log(1650);try{r&&(window.RTCPeerConnection=r,console.log(1651)),s&&navigator.mediaDevices&&(navigator.mediaDevices.getUserMedia=s,console.log(1652)),d=!1,sessionStorage.setItem("plugin_user_expired","true"),sessionStorage.setItem("plugin_manually_activated","false"),console.log(1653),console.log(1654)}catch(t){console.error(1504)}}()):t.data&&"ENABLE_PLUGIN_RECOVERED"===t.data.type&&(console.log(1630),console.log(1631),console.log(1632),console.log(1633),console.log(1634),function(){console.log("🔄 开始恢复插件功能...");try{d=!0,sessionStorage.removeItem("plugin_user_expired"),sessionStorage.setItem("plugin_manually_activated","true"),console.log("✅ 插件状态已设置为激活"),console.log("🔄 重新初始化WebRTC拦截..."),M(),console.log("🎯 插件功能已完全恢复"),console.log("💡 用户现在可以正常使用伪装功能")}catch(t){console.error("❌ 恢复插件功能时出错:",t)}}())});const f=Intl.DateTimeFormat.prototype.resolvedOptions;Intl.DateTimeFormat.prototype.resolvedOptions=function(){const t=f.call(this);return t.timeZone=U.timezone,t.locale=U.locale,t};Date.prototype.getTimezoneOffset;Date.prototype.getTimezoneOffset=function(){return u};const w=Date,T=Date.prototype.toString,h=(Date.prototype.toDateString,Date.prototype.toTimeString,Date.prototype.toLocaleString);Date.prototype.toString=function(){const t=this.getTime(),o=u,n=new w(t-60*o*1e3),a=function(t,e,o){const n=new w(e-60*o*60*1e3);return`${t}(GMT)Local time: ${n.getFullYear()}-${String(n.getMonth()+1).padStart(2,"0")}-${String(n.getDate()).padStart(2,"0")} ${String(n.getHours()).padStart(2,"0")}:${String(n.getMinutes()).padStart(2,"0")}:${String(n.getSeconds()).padStart(2,"0")}`}(e,t,o/60);return T.call(n).replace(/GMT[+-]\d{4}.*$/,a)},Date.prototype.toLocaleString=function(t,e){(e=e||{}).timeZone=U.timezone;const o=t||U.locale,n=h.call(this,o,e);return console.log(1640),n};const _=window.Date;window.Date=function(...t){let e;return e=0===t.length?new _:new _(...t),e.getTimezoneOffset=function(){return u},e.toString=Date.prototype.toString,e.toDateString=Date.prototype.toDateString,e.toTimeString=Date.prototype.toTimeString,e.toLocaleString=Date.prototype.toLocaleString,e.toLocaleDateString=Date.prototype.toLocaleDateString,e.toLocaleTimeString=Date.prototype.toLocaleTimeString,e},Object.setPrototypeOf(window.Date,_),Object.setPrototypeOf(window.Date.prototype,_.prototype),window.Date.now=_.now,window.Date.parse=_.parse,window.Date.UTC=_.UTC;const C=Intl.DateTimeFormat;Intl.DateTimeFormat=function(t,e){return(e=e||{}).timeZone=U.timezone,new C(t=t||U.locale,e)},Intl.DateTimeFormat.prototype=C.prototype,Intl.DateTimeFormat.supportedLocalesOf=C.supportedLocalesOf;const D={getCurrentPosition:function(t,e,o){if(t){const e={coords:{latitude:p,longitude:m,accuracy:U.accuracy+i,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()};console.log(1660),setTimeout(()=>t(e),100)}},watchPosition:function(t,e,o){if(t){const e={coords:{latitude:p,longitude:m,accuracy:U.accuracy+Math.floor(20*Math.random())-10,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()};setTimeout(()=>t(e),100)}return Math.floor(1e3*Math.random())+1},clearWatch:function(t){}};try{try{try{delete navigator.geolocation}catch(t){}Object.defineProperty(navigator,"geolocation",{get:function(){return console.log(1670),D},set:function(t){console.log(1671)},configurable:!0,enumerable:!0}),console.log(1672)}catch(t){console.warn(1673)}try{navigator.geolocation&&"object"==typeof navigator.geolocation&&(navigator.geolocation.getCurrentPosition=D.getCurrentPosition,navigator.geolocation.watchPosition=D.watchPosition,navigator.geolocation.clearWatch=D.clearWatch,console.log(1674))}catch(t){console.warn(1675)}console.log(1676),setTimeout(()=>{try{navigator.geolocation&&navigator.geolocation.getCurrentPosition&&navigator.geolocation.getCurrentPosition(t=>{console.log(1680)},t=>{console.log(1681)})}catch(t){console.warn(1682)}},1e3)}catch(t){console.error(1504)}try{const t=U.locale,e=t.split("-")[0],o=[t,e];o.includes("en")||o.push("en"),o.includes("en-US")||o.push("en-US"),Object.defineProperty(navigator,"language",{get:function(){return t},configurable:!0}),Object.defineProperty(navigator,"languages",{get:function(){return o},configurable:!0}),console.log(1690)}catch(t){console.warn(1691)}const P=c.webrtcIPRange+c.webrtcLastOctet;function v(){if(d)return!!window.RTCPeerConnection&&(r||(r=window.RTCPeerConnection),window.RTCPeerConnection=function(t){if(!d)return console.log("🚫 插件已停用，使用原始RTCPeerConnection"),new r(t);const e={...t,iceServers:[],iceCandidatePoolSize:0},o=new r(e),n=o.onicecandidate;o.onicecandidate=function(t){if(t.candidate){const e={...t.candidate,candidate:`candidate:1 1 UDP 2130706431 ${P} 54400 typ host generation 0`,address:P,ip:P},o={...t,candidate:e};n&&n.call(this,o)}else n&&n.call(this,t)};const a=o.addEventListener;return o.addEventListener=function(type,t,e){if("icecandidate"===type){const o=function(e){if(e.candidate){const o={...e.candidate,candidate:`candidate:1 1 UDP 2130706431 ${P} 54400 typ host generation 0`,address:P,ip:P},n={...e,candidate:o};t.call(this,n)}else t.call(this,e)};return a.call(this,type,o,e)}return a.call(this,type,t,e)},o},window.RTCPeerConnection.prototype=r.prototype,window.webkitRTCPeerConnection&&(window.webkitRTCPeerConnection=window.RTCPeerConnection),window.mozRTCPeerConnection&&(window.mozRTCPeerConnection=window.RTCPeerConnection),g=!0,!0);console.log(1720)}function M(){try{if("true"===sessionStorage.getItem("plugin_user_expired"))return console.log(1105),void(d=!1);if(!d)return void console.log(1720);v()?console.log(1730):console.log(1731)}catch(t){console.log(1731)}}console.log(1710),M();let R=0;const A=setInterval(()=>{if(R++,R>6)return void clearInterval(A)},5e3);"loading"===document.readyState&&document.addEventListener("DOMContentLoaded",M),window.addEventListener("load",M);const O=Object.defineProperty;Object.defineProperty=function(t,e,o){if(t===window&&("RTCPeerConnection"===e||"webkitRTCPeerConnection"===e||"mozRTCPeerConnection"===e)){const n=O.call(this,t,e,o);return setTimeout(M,0),n}return O.call(this,t,e,o)};try{void 0!==navigator.deviceMemory&&(Object.defineProperty(navigator,"deviceMemory",{get:function(){return c.deviceMemory},configurable:!0}),console.log(1750));const t=WebGLRenderingContext.prototype.getParameter;if(WebGLRenderingContext.prototype.getParameter=function(e){return e===this.VENDOR?c.webglVendor:e===this.RENDERER?c.webglRenderer:t.call(this,e)},window.WebGL2RenderingContext){const t=WebGL2RenderingContext.prototype.getParameter;WebGL2RenderingContext.prototype.getParameter=function(e){return e===this.VENDOR?c.webglVendor:e===this.RENDERER?c.webglRenderer:t.call(this,e)}}console.log(1760)}catch(t){console.warn(1761)}("true"===sessionStorage.getItem("plugin_user_expired")?(console.log(1105),0):(console.log(1700),1))&&(console.log(1770),console.log(1771),console.log(1601),console.log(1602),console.log(1772),console.log(1603),console.log(1773),console.log(1774),console.log(1775),console.log(1776),console.log(1300),console.log(1304),console.log(1305),console.log(1306),console.log(1307),console.log(1308),console.log(1309),console.log("==============================================="));let I=0;const L=setInterval(()=>{I++,I>5?clearInterval(L):window.RTCPeerConnection&&-1===window.RTCPeerConnection.toString().indexOf("fakeIP")&&M()},1e3)}();