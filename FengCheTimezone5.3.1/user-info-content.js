!function(){"use strict";function e(e="new"){try{"true"===sessionStorage.getItem("plugin_user_expired")&&(console.log("🔄 检测到用户状态已恢复，发送激活指令..."),console.log(`   数据类型: ${e}`),window.postMessage({type:"ENABLE_PLUGIN_RECOVERED",source:"user-info-content",dataType:e,timestamp:Date.now(),reason:"user_recovered"},"*"),sessionStorage.removeItem("plugin_user_expired"),sessionStorage.setItem("plugin_manually_activated","true"),console.log("✅ 已发送恢复指令并清除过期标记"))}catch(e){console.error("❌ 检查恢复状态失败:",e)}}window.USER_INFO_CONTENT_LOADED||(window.USER_INFO_CONTENT_LOADED=!0,setTimeout(()=>{try{if("undefined"==typeof chrome||!chrome.storage||!chrome.storage.local)return void console.log(1208);chrome.storage.local.get(null,o=>{if(chrome.runtime.lastError)console.log(1209);else{if(o.plugin_user_data){const n=o.plugin_user_data;let i=null,s=n.remaining_days;if(n.expires_at)try{const e={calculatePreciseTimeInfo:function(e){if(!e)return{isExpired:!0,remainingMs:0,remainingMinutes:0,remainingHours:0,remainingDays:0,expireTime:null,currentTime:null};try{const now=new Date,o=new Date(e);console.log("🕐 精确时间计算 (Content Script):"),console.log(`   当前时间: ${now.toLocaleString()} (${now.getTime()})`),console.log(`   过期时间: ${o.toLocaleString()} (${o.getTime()})`);const n=o.getTime()-now.getTime();console.log(`   时间差: ${n}ms`);if(n<=0)return console.log("   ⚠️ 已过期"),{isExpired:!0,remainingMs:0,remainingMinutes:0,remainingHours:0,remainingDays:0,expireTime:o,currentTime:now};const i=n,s=Math.floor(n/1e3),t=Math.floor(n/6e4),r=Math.floor(n/36e5),l=Math.ceil(n/864e5);return console.log(`   剩余: ${l}天 ${r%24}小时 ${t%60}分钟 ${s%60}秒`),{isExpired:!1,remainingMs:i,remainingSeconds:s,remainingMinutes:t,remainingHours:r,remainingDays:Math.max(0,l),expireTime:o,currentTime:now}}catch(e){return console.error("❌ 精确时间计算失败:",e),{isExpired:!0,remainingMs:0,remainingMinutes:0,remainingHours:0,remainingDays:0,expireTime:null,currentTime:null,error:e.message}}}}.calculatePreciseTimeInfo(n.expires_at);i={remainingDays:e.remainingDays,remainingHours:e.remainingHours,remainingMinutes:e.remainingMinutes,remainingSeconds:e.remainingSeconds,isExpired:e.isExpired,expireDate:e.expireTime?e.expireTime.toLocaleString():null,timeInfo:e,calculationMethod:"precise_content_script"},s=i.remainingDays}catch(e){console.error("❌ 过期时间计算失败:",e)}if(console.log("🌍 FengCheTimezone by xfish - 用户信息 (新格式)"),console.log(`👤 用户: ${n.username}`),console.log(`📅 剩余天数: ${s} 天`),i&&i.timeInfo&&!i.isExpired){const e=i.timeInfo,o=Math.floor(e.remainingHours/24),n=e.remainingHours%24,s=e.remainingMinutes%60,t=e.remainingSeconds%60;o>0?console.log(`⏱️ 精确剩余: ${o}天 ${n}小时 ${s}分钟 ${t}秒`):n>0?console.log(`⏱️ 精确剩余: ${n}小时 ${s}分钟 ${t}秒`):s>0?console.log(`⏱️ 精确剩余: ${s}分钟 ${t}秒`):console.log(`⏱️ 精确剩余: ${t}秒`)}if(i&&i.expireDate)console.log(`📆 过期时间: ${i.expireDate}`);else if(n.expires_at)try{const e=new Date(n.expires_at).toLocaleString();console.log(`📆 过期时间: ${e}`)}catch(e){console.log(`📆 过期时间: ${n.expires_at} (原始格式)`)}console.log(`📊 状态: ${n.status}`),console.log(`⏰ 最后更新: ${new Date(n.last_updated||n.login_time).toLocaleString()}`);if(i?i.isExpired:s<=0){console.log("⚠️ 用户账户已过期，插件功能已停用"),console.log("📤 发送停用指令到 MAIN 世界..."),window.postMessage({type:"DISABLE_PLUGIN_EXPIRED",source:"user-info-content",timestamp:Date.now(),reason:"user_expired"},"*");try{sessionStorage.setItem("plugin_user_expired","true"),sessionStorage.setItem("plugin_manually_activated","false"),console.log("✅ 已设置过期状态标记")}catch(e){console.error("❌ 设置过期标记失败:",e)}}else s<=7?(console.log(`⚠️ 用户账户即将过期，剩余 ${s} 天`),e()):(console.log("✅ 用户账户正常，插件功能可用"),e());return}if(o.user_data){const n=o.user_data,i=n.login_time||o.login_time;if(console.log("🌍 FengCheTimezone by xfish - 用户信息 (旧格式)"),console.log(`👤 用户: ${n.username}`),console.log(`📅 剩余天数: ${n.remaining_days} 天`),i&&n.remaining_days>0)try{const e=new Date(i).getTime()+24*n.remaining_days*60*60*1e3,o=new Date(e);console.log(`📆 预计过期时间: ${o.toLocaleString()} (基于登录时间计算)`)}catch(e){console.log("📆 无法计算具体过期时间")}if(console.log(`📊 状态: ${n.status}`),console.log(`⏰ 登录时间: ${new Date(i).toLocaleString()}`),n.remaining_days<=0){console.log("⚠️ 用户账户已过期，插件功能已停用"),console.log("📤 发送停用指令到 MAIN 世界 (旧格式数据)..."),window.postMessage({type:"DISABLE_PLUGIN_EXPIRED",source:"user-info-content-legacy",timestamp:Date.now(),reason:"user_expired_legacy"},"*");try{sessionStorage.setItem("plugin_user_expired","true"),sessionStorage.setItem("plugin_manually_activated","false"),console.log("✅ 已设置过期状态标记 (旧格式)")}catch(e){console.error("❌ 设置过期标记失败:",e)}}else n.remaining_days<=7?(console.log(`⚠️ 用户账户即将过期，剩余 ${n.remaining_days} 天`),e("legacy")):(console.log("✅ 用户账户正常，插件功能可用"),e("legacy"));return void console.log("💡 检测到旧格式数据，建议重新登录以使用新的统一存储格式")}if(o.auth_user_info){const n=o.auth_user_info;return console.log("🌍 FengCheTimezone by xfish - 用户信息 (认证格式)"),console.log(`👤 用户: ${n.username}`),console.log(`📅 剩余天数: ${n.remaining_days} 天`),console.log(`📊 状态: ${n.status}`),console.log(`⏰ 更新时间: ${new Date(o.auth_updated_at).toLocaleString()}`),n.remaining_days<=0?console.log("⚠️ 用户账户已过期，插件功能已停用"):n.remaining_days<=7?(console.log(`⚠️ 用户账户即将过期，剩余 ${n.remaining_days} 天`),e("auth")):(console.log("✅ 用户账户正常，插件功能可用"),e("auth")),void console.log("💡 检测到认证格式数据，建议重新登录以使用新的统一存储格式")}if(console.log(1910),0===Object.keys(o).length){console.log(1911),console.log(1912),window.postMessage({type:"DISABLE_PLUGIN_EXPIRED",source:"user-info-content-no-user",timestamp:Date.now(),reason:"no_user_data"},"*");try{sessionStorage.setItem("plugin_user_expired","true"),sessionStorage.setItem("plugin_manually_activated","false"),console.log(1913)}catch(e){console.error(1504)}}}})}catch(e){console.log(1914)}},1e3))}();